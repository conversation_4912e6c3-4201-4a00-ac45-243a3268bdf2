
import {
    Engine,             // 渲染引擎
    Scene,              // 3D场景
    Vector3,            // 三维向量
    Color3,             // RGB颜色（用于材质）
    Color4,             // RGBA颜色（用于清除颜色）
    HemisphericLight,   // 半球光（环境光）
    ArcRotateCamera,    // 弧度旋转相机（适合第三人称观察）
    MeshBuilder,        // 网格构建器
    StandardMaterial,   // 标准材质
    PhysicsBody,        // 物理体（用于将网格转换为物理对象）
    PhysicsShapeSphere,
    PhysicsMotionType,  // 物理运动类型（静态、动态、运动学）
    PhysicsShapeBox,    // 盒形物理碰撞体
    Quaternion,         // 四元数（用于旋转，这里用于物理形状的旋转）
    CreateSphere,
    CreateGround,
    HavokPlugin,
 } from "@babylonjs/core";
import { WebGPUEngine } from "@babylonjs/core/Engines/webgpuEngine";
import HavokPhysics from "@babylonjs/havok";
import "@babylonjs/core/Physics/physicsEngineComponent";
// If you don't need the standard material you will still need to import it since the scene requires it.
import "@babylonjs/core/Materials/standardMaterial";

import Player from './entities/player.js';
import networkManager from './network/networkManager.js';
import InputManager from './input/inputManager.js';

// 全局变量，用于存储引擎和场景实例，方便其他模块访问
let engine;
let scene;
let localPlayer = null;     // 本地玩家实例，初始为null，待服务器分配ID后再创建
let localPlayerId = "pending_auth"; // 本地玩家的ID，初始为待认证状态，由服务器分配
let inputManager;           // 输入管理器实例
const remotePlayers = new Map(); // 用于存储远程玩家的 Map (以ID为键，Player实例为值)

//const havokModule = HavokPlugin();

// 每秒发送玩家输入到服务器的频率（例如，每秒10次）
const SERVER_INPUT_SEND_INTERVAL_MS = 100;
let lastInputSendTime = 0; // 上次发送输入的时间

export const createScene = async () => {
    // Get the canvas element
    const canvas = document.getElementById("renderCanvas");
    if (!canvas) {
        console.error("未能找到ID为'renderCanvas'的canvas元素, 请检查HTML文件.");
        return null; // 返回null表示场景创建失败
    }

    // Generate the BABYLON 3D engine, 访问则使用webgpu引擎: http://localhost:8080/?engine=webgpu
    const engineType = location.search.split("engine=")[1]?.split("&")[0] || "webgl";
    if (engineType === "webgpu") {
        const webGPUSupported = await WebGPUEngine.IsSupportedAsync;
        if (webGPUSupported) {
            // You can decide which WebGPU extensions to load when creating the engine. I am loading all of them
            await import("@babylonjs/core/Engines/WebGPU/Extensions/");
            const webgpu = engine = new WebGPUEngine(canvas, {
                adaptToDeviceRatio: true,
                antialias: true,
            });
            await webgpu.initAsync();
            engine = webgpu;
        } else {
            // 创建Babylon引擎实例。第二个参数为true表示开启抗锯齿。
            engine = new Engine(canvas, true, { preserveDrawingBuffer: true, stencil: true });
        }
    } else {
        // 创建Babylon引擎实例。第二个参数为true表示开启抗锯齿。
        engine = new Engine(canvas, true, { preserveDrawingBuffer: true, stencil: true });
    }

    // Create the scene
    scene = new Scene(engine);

    // --- 相机设置 ---
    // 创建一个ArcRotateCamera（弧度旋转相机），命名为"camera"
    // 参数：name, alpha, beta, radius, target, scene
    // alpha: 绕Y轴的弧度（水平旋转），Math.PI / 4 = 45度
    // beta: 绕X轴的弧度（垂直旋转），Math.PI / 3 = 60度
    // radius: 相机距离目标的距离
    // target: 相机聚焦的目标点 (Vector3.Zero() 表示场景中心)
    const camera = new ArcRotateCamera("camera", Math.PI / 4, Math.PI / 3, 30, Vector3.Zero(), scene);
    camera.setTarget(Vector3.Zero());   // targets the camera to scene origin
    camera.attachControl(canvas, true); // 将相机控件附加到canvas上，允许用户通过鼠标/触摸来控制相机
    // 设置相机视野半径限制，防止相机过近或过远，影响视觉体验
    camera.lowerRadiusLimit = 5;  // 最小半径
    camera.upperRadiusLimit = 100; // 最大半径
    camera.wheelPrecision = 0.5;  // 调整鼠标滚轮缩放的灵敏度

    // --- 光照设置 ---
    // 创建一个HemisphericLight（半球光），命名为"light1"
    // 模拟环境光和天空光，光线方向为 (1, 1, 0)
    const light1 = new HemisphericLight("light1", new Vector3(1, 1, 0), scene);
    light1.intensity = 0.7; // 调整光照强度
    light1.diffuse = new Color3(0.8, 0.75, 0.9); // 设置漫反射颜色(地面、物体受光面颜色)为柔和的浅紫色/粉色，营造神秘感
    light1.specular = new Color3(0.5, 0.5, 0.5); // 设置镜面反射颜色为标准灰色镜面反射

    // 物理引擎
    console.log("正在加载Havok WASM模块...");
    const havokInstance = await HavokPhysics();
    console.log("Havok WASM模块加载完成!");
    const havokPlugin = new HavokPlugin(true, havokInstance);  // 使用加载的Havok模块实例创建Havok插件
    console.log("在场景中启用Havok物理引擎...");
    scene.enablePhysics(new Vector3(0, -9.81, 0), havokPlugin); // 在场景中启用物理引擎
    console.log("Havok物理引擎已启用!");

    // 场景物体 - 球体碰撞模型
    const sphere = CreateSphere("sphere", { diameter: 2, segments: 32 }, scene); // 球体网格模型
    sphere.position.y = 4; // 向上移动4单位
    const sphereShape = new PhysicsShapeSphere(new Vector3(0, 0, 0), 1, scene); // Create a sphere shape
    const sphereBody = new PhysicsBody(sphere, PhysicsMotionType.DYNAMIC, false, scene); // Sphere body
    sphereShape.material = { friction: 0.2, restitution: 0.6 }; // Set shape material properties
    sphereBody.shape = sphereShape; // Associate shape and body
    sphereBody.setMassProperties({ mass: 1 }); // And body mass

    // --- 地面平面 ---
    // 使用MeshBuilder创建一个大型的地面网格，命名为"ground"
    // 宽度和高度都设置为200单位
    const ground = MeshBuilder.CreateGround("ground", { width: 200, height: 200 }, scene);
    const groundMaterial = new StandardMaterial("groundMaterial", scene); // 创建一个StandardMaterial（标准材质），命名为"groundMaterial"
    groundMaterial.diffuseColor = new Color3(0.2, 0.3, 0.1); // 设置地面的漫反射颜色为深绿色，模拟土地或苔藓
    ground.material = groundMaterial; // 将材质应用到地面网格

    // --- 为地面添加物理属性 ---
    // 创建一个物理体PhysicsBody，关联到ground网格
    // PhysicsMotionType.STATIC 表示地面是一个静态物体，不受力影响，但可以与动态物体发生碰撞
    // 第三个参数 false 表示这个物理体不是从一个PhysicsAggregate创建的
    const groundPhysicsBody = new PhysicsBody(ground, PhysicsMotionType.STATIC, false, scene);

    // 定义地面的物理碰撞形状为盒子形状（PhysicsShapeBox）
    const groundShape = new PhysicsShapeBox(
        new Vector3(0, 0, 0),         // 形状中心（相对于网格的局部坐标原点）
        new Quaternion(0, 0, 0, 1),   // 形状旋转（单位四元数，表示无旋转）
        new Vector3(200, 0.1, 200),   // 形状尺寸：宽200，厚度0.1（模拟薄地面），深200
        scene                         // 场景实例
    );
    groundShape.material = { friction: 0.5, restitution: 0.1 }; // // 设置地面的物理材质属性：摩擦力（friction）和弹性（restitution）
    groundPhysicsBody.shape = groundShape; // 将创建的形状赋予地面的物理体
    groundPhysicsBody.setMassProperties({ mass: 0 }); // Set the mass to 0



    // --- 玩家实体初始化 ---
    // 为本地玩家生成一个临时ID
    const localPlayerId = "localUser_" + Math.random().toString(36).substring(2, 9);
    // 创建本地玩家实例，初始位置设置在 (0, 2, 0)
    const localPlayer = new Player(localPlayerId, scene, new Vector3(0, 2, 0), true);

    // --- 输入管理器初始化 ---
    inputManager = new InputManager(scene);
    inputManager.init(); // 初始化输入管理器，开始监听键盘事件

    // --- 场景渲染循环前观察者 ---
    // 在每帧渲染前调用 localPlayer.update() 方法，处理玩家的移动和旋转
    scene.onBeforeRenderObservable.add(() => {
        // 获取自上一帧以来的时间增量（秒），用于帧率无关的运动
        const deltaTime = engine.getDeltaTime() / 1000.0;
        localPlayer.update(deltaTime);
        // 更新相机目标，使其跟随本地玩家
        camera.setTarget(localPlayer.mesh.position);
    });

    // JUST FOR TESTING. Not needed for anything else
    window.scene = scene;
    window.networkManager = networkManager;

    // Register a render loop to repeatedly render the scene
    engine.runRenderLoop(function () {
        scene.render();
    });

    // Watch for browser/canvas resize events
    window.addEventListener("resize", function () {
        engine.resize();
    });

    console.log("场景基础元素、物理引擎、本地玩家和输入管理器已设置完毕。");

    return scene; // 返回创建好的场景
};

createScene().then((createdScene) => {
    // 只有当场景成功创建时才启动渲染循环和事件监听
    if (createdScene) {
        // --- 注册网络事件处理器 ---
        registerNetworkHandlers(createdScene);

        // 连接到游戏服务器
        console.log("连接服务器...")
        networkManager.connect();

        // --- 渲染循环 ---
        engine.runRenderLoop(() => {
            const deltaTime = engine.getDeltaTime() / 1000.0; // 获取自上一帧以来的时间增量（秒）

            // --- 本地玩家更新和输入处理 ---
            if (localPlayer && localPlayer.isLocalPlayer) {
                // 从输入管理器获取当前的移动输入向量
                const movementInput = inputManager.update();

                // 将移动输入应用到本地玩家的逻辑中
                localPlayer.applyMovementInput(movementInput);

                // 检查跳跃输入
                if (inputManager.getJumpInput()) {
                    // 假设玩家在地面上才能跳跃，这里简化处理，未来需要更严谨的地面检测
                    // 检查Y轴速度接近0（或正在下落），并确保不是在向上运动
                    if (localPlayer.physicsBody.getLinearVelocity().y <= 0.1) {
                        // 向上施加一个瞬时力（冲量）
                        localPlayer.physicsBody.applyImpulse(new Vector3(0, 5, 0), localPlayer.mesh.position);
                        inputManager.resetJumpInput(); // 重置跳跃输入状态，避免连续跳跃
                    }
                }

                // 更新本地玩家的物理和动画状态
                localPlayer.update(deltaTime);

                // 更新相机目标，使其跟随本地玩家
                createdScene.activeCamera.setTarget(localPlayer.mesh.position);

                // --- 将玩家输入同步到服务器 ---
                // 每隔一段时间（例如 100ms）发送一次玩家输入到服务器，避免过于频繁的发送
                const currentTime = performance.now();
                if (currentTime - lastInputSendTime > SERVER_INPUT_SEND_INTERVAL_MS) {
                    // 如果有任何移动输入，或者需要周期性同步位置
                    // 这里我们发送玩家的当前位置、旋转和输入（以便服务器进行验证和模拟）
                    networkManager.send("player_movement_update", {
                        // 发送世界坐标系下的移动输入
                        inputX: movementInput.x,
                        inputZ: movementInput.z,
                        // 发送玩家的当前位置和旋转（以便服务器同步或验证）
                        position: localPlayer.mesh.position.asArray(),
                        rotation: localPlayer.mesh.rotationQuaternion.asArray()
                        // 更多状态如：isJumping: inputManager.getJumpInput()
                    });
                    lastInputSendTime = currentTime;
                }
            }

            // --- 远程玩家更新 (由服务器消息驱动) ---
            remotePlayers.forEach(player => player.update(deltaTime));

            // 渲染场景
            createdScene.render();
        });

        // --- 窗口尺寸变化处理 ---
        // 监听浏览器窗口尺寸变化事件，并通知Babylon引擎调整渲染尺寸
        window.addEventListener("resize", () => {
            engine.resize();
        });
    }
}).catch(error => {
    // 捕获场景创建过程中的任何错误
    console.error("场景创建失败:", error);
});


/**
 * 注册网络事件处理器，处理来自服务器的各种消息。
 * @param {BABYLON.Scene} scene - Babylon.js 场景实例。
 */
function registerNetworkHandlers(scene) {
    // 假设服务器分配客户端唯一ID和初始位置
    // 消息格式: { type: "client_identity_ack", payload: { id: "unique_server_id", position: [x,y,z], rotation: [qx,qy,qz,qw] } }
    networkManager.on("client_identity_ack", (payload) => {
        console.log("收到服务器分配的身份ID:", payload.id);
        localPlayerId = payload.id; // 更新本地玩家ID

        // 如果本地玩家尚未创建，则根据服务器分配的ID和初始位置创建
        if (!localPlayer) {
            localPlayer = new Player(
                localPlayerId,
                scene,
                new Vector3(payload.position[0], payload.position[1], payload.position[2]),
                true // 标记为本地玩家
            );
            console.log(`本地玩家 '${localPlayerId}' 已创建。`);
        } else {
            // 如果玩家已用临时ID创建，更新其正式ID并同步服务器提供的初始状态
            console.log(`本地玩家ID从 'pending_auth' 更新为 '${localPlayerId}'。`);
            localPlayer.id = localPlayerId;
            localPlayer.setPositionAndRotation(
                new Vector3(payload.position[0], payload.position[1], payload.position[2]),
                new Quaternion(payload.rotation[0], payload.rotation[1], payload.rotation[2], payload.rotation[3])
            );
        }
        // 如果相机目标已经设置，确保它指向新的 localPlayer
        if (scene.activeCamera) {
            scene.activeCamera.setTarget(localPlayer.mesh.position);
        }

        // 告知服务器本地玩家已准备好接收世界状态
        networkManager.send("client_ready", { id: localPlayerId });
    });

    // 处理其他玩家加入游戏的消息
    // 消息格式: { type: "player_joined", payload: { id: "player_id", position: [x,y,z], rotation: [qx,qy,qz,qw], ... } }
    networkManager.on("player_joined", (payload) => {
        // 如果是本地玩家自己加入（服务器广播），则忽略
        if (payload.id === localPlayerId) {
            return;
        }
        // 如果远程玩家已存在，也忽略（避免重复创建）
        if (remotePlayers.has(payload.id)) {
            console.warn(`收到重复的玩家加入消息: ${payload.id}`);
            return;
        }

        console.log(`新玩家 '${payload.id}' 加入游戏。`);
        // 创建新的远程玩家实例
        const newPlayer = new Player(
            payload.id,
            scene,
            new Vector3(payload.position[0], payload.position[1], payload.position[2]),
            false // 标记为远程玩家
        );
        // 设置初始旋转（如果提供）
        if (payload.rotation) {
            newPlayer.mesh.rotationQuaternion = new Quaternion(payload.rotation[0], payload.rotation[1], payload.rotation[2], payload.rotation[3]);
        }
        remotePlayers.set(payload.id, newPlayer); // 将新玩家添加到远程玩家Map中
    });

    // 处理其他玩家离开游戏的消息
    // 消息格式: { type: "player_left", payload: { id: "player_id" } }
    networkManager.on("player_left", (payload) => {
        if (remotePlayers.has(payload.id)) {
            console.log(`玩家 '${payload.id}' 离开游戏。`);
            remotePlayers.get(payload.id).dispose(); // 销毁玩家模型及其物理体
            remotePlayers.delete(payload.id);      // 从Map中删除
        } else {
            console.warn(`收到未知玩家 '${payload.id}' 离开的消息。`);
        }
    });

    // 处理批量实体（玩家、NPC等）状态更新消息
    // 消息格式: { type: "entity_updates", payload: { updates: [ { id: "id1", position: [x,y,z], rotation: [qx,qy,qz,qw] }, ... ] } }
    networkManager.on("entity_updates", (payload) => {
        if (!payload || !Array.isArray(payload.updates)) {
            console.warn("收到的实体更新消息格式不正确:", payload);
            return;
        }

        payload.updates.forEach(update => {
            // 忽略本地玩家的更新，本地玩家的位置和旋转由客户端权威控制（虽然服务器会验证）
            if (update.id === localPlayerId) {
                // 客户端预测-服务器校正模型可以在这里实现更复杂的对账逻辑
                // 例如，如果服务器返回的本地玩家位置与客户端模拟的位置偏差过大，进行校正
                // 但对于MMORPG的实时性，通常会信任客户端的输入，服务器只做验证
                // console.log("收到本地玩家的服务器更新（忽略）。");
                return;
            }

            const player = remotePlayers.get(update.id);
            if (player) {
                // 如果是远程玩家，更新其位置和旋转
                // TODO: 未来这里应该实现平滑插值，而不是直接设置，以减少抖动
                player.setPositionAndRotation(
                    new Vector3(update.position[0], update.position[1], update.position[2]),
                    new Quaternion(update.rotation[0], update.rotation[1], update.rotation[2], update.rotation[3])
                );
            } else {
                // 如果收到未知实体的更新，可能是新加入的实体，但 "player_joined" 消息通常会先一步
                // 对于未知ID的实体，可以根据类型判断是否创建，或等待明确的创建消息
                console.warn(`收到未知实体ID '${update.id}' 的更新。`);
                // 未来考虑在这里根据 update.type 字段（如果存在）创建其他类型的实体
                // 例如：if (update.type === "player") { /* ... create player ... */ }
            }
        });
    });

    // 假设服务器会周期性发送一个包含所有当前玩家和NPC的“快照”
    // 这对于客户端刚连接或需要大规模同步时非常有用
    // 消息格式: { type: "world_snapshot", payload: { players: [{id, pos, rot}, ...], npcs: [...]} }
    networkManager.on("world_snapshot", (payload) => {
        console.log("收到世界状态快照。");
        const serverPlayers = new Set(); // 用于追踪快照中存在的玩家ID

        payload.players.forEach(playerData => {
            serverPlayers.add(playerData.id);
            if (playerData.id === localPlayerId) {
                // 客户端的本地玩家以客户端为权威，服务器快照仅作参考或校正
                // console.log("收到本地玩家的快照更新（忽略）。");
                return;
            }

            let player = remotePlayers.get(playerData.id);
            if (!player) {
                // 如果是快照中新的远程玩家，则创建
                console.log(`快照中发现新玩家 '${playerData.id}'，正在创建。`);
                player = new Player(
                    playerData.id,
                    scene,
                    new Vector3(playerData.position[0], playerData.position[1], playerData.position[2]),
                    false
                );
                if (playerData.rotation) {
                    player.mesh.rotationQuaternion = new Quaternion(playerData.rotation[0], playerData.rotation[1], playerData.rotation[2], playerData.rotation[3]);
                }
                remotePlayers.set(playerData.id, player);
            } else {
                // 现有玩家，更新其状态
                player.setPositionAndRotation(
                    new Vector3(playerData.position[0], playerData.position[1], playerData.position[2]),
                    new Quaternion(playerData.rotation[0], playerData.rotation[1], playerData.rotation[2], playerData.rotation[3])
                );
            }
        });

        // 移除在快照中不再存在的远程玩家
        remotePlayers.forEach((player, id) => {
            if (id !== localPlayerId && !serverPlayers.has(id)) {
                console.log(`快照中未找到玩家 '${id}'，将其移除。`);
                player.dispose();
                remotePlayers.delete(id);
            }
        });
    });

    // 可以在这里注册其他消息类型，例如聊天消息、物品拾取等
    // networkManager.on("chat_message", (payload) => { /* ... */ });
}
