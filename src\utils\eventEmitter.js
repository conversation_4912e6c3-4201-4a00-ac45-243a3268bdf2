/**
 * EventEmitter 类，用于在浏览器中实现事件驱动编程。
 * 支持命名空间和精确事件匹配。
 */
class EventEmitter {
    /**
     * 构造函数，初始化事件存储对象和当前名字空间。
     * @constructor
     * @param {string} [namespace=""] - 初始化的名字空间，默认为空字符串。
     */
    constructor(namespace = "") {
      this.events = {}; // 存储事件名和对应的监听器数组
      this.currentNamespace = namespace; // 当前名字空间
    }

    /**
     * 设置当前命名空间。
     * @param {string} namespace - 命名空间。
     * @returns {EventEmitter} 返回实例以支持链式调用。
     */
    namespace(namespace) {
      this.currentNamespace = namespace;
      return this;
    }

    /**
     * 获取完整的事件名，包含命名空间。
     * @param {string} eventName - 事件名。
     * @returns {string} 完整的事件名。
     */
    getFullEventName(eventName) {
      if (this.currentNamespace && !eventName.startsWith(this.currentNamespace)) {
        return `${this.currentNamespace}.${eventName}`;
      }
      return eventName;
    }

    /**
     * 注册事件监听器。
     * @param {string} eventName - 事件名。
     * @param {Function} callback - 监听器函数。
     */
    on(eventName, callback) {
      const fullEventName = this.getFullEventName(eventName);
      if (!this.events[fullEventName]) {
        this.events[fullEventName] = [];
      }
      this.events[fullEventName].push(callback);
    }

    /**
     * 注册一次性事件监听器，触发一次后自动移除。
     * @param {string} eventName - 事件名。
     * @param {Function} callback - 监听器函数。
     */
    once(eventName, callback) {
      const fullEventName = this.getFullEventName(eventName);
      const onceCallback = (...args) => {
        callback(...args);
        this.off(fullEventName, onceCallback);
      };
      this.on(fullEventName, onceCallback);
    }

    /**
     * 触发事件，调用所有注册的监听器。
     * @param {string} eventName - 事件名。
     * @param {...any} args - 传递给监听器的参数。
     */
    emit(eventName, ...args) {
      const fullEventName = this.getFullEventName(eventName);
      const callbacks = this.events[fullEventName];
      if (callbacks) {
        callbacks.forEach((callback) => {
          try {
            callback(...args);
          } catch (error) {
            console.error(`EventEmitter: 事件 "${fullEventName}" 的监听器执行出错:`, error);
          }
        });
      }
    }

    /**
     * 移除指定事件的监听器。
     * @param {string} eventName - 事件名。
     * @param {Function} callback - 要移除的监听器函数。
     */
    off(eventName, callback) {
      const fullEventName = this.getFullEventName(eventName);
      const callbacks = this.events[fullEventName];
      if (callbacks) {
        this.events[fullEventName] = callbacks.filter((cb) => cb !== callback);
      }
    }

    /**
     * 移除指定事件的所有监听器。
     * @param {string} eventName - 事件名。
     */
    removeAllListeners(eventName) {
      const fullEventName = this.getFullEventName(eventName);
      delete this.events[fullEventName];
    }

    /**
     * 获取指定事件的所有监听器。
     * @param {string} eventName - 事件名。
     * @returns {Function[]} 监听器数组。
     */
    listeners(eventName) {
      const fullEventName = this.getFullEventName(eventName);
      return this.events[fullEventName] || [];
    }
  }

  // 导出 EventEmitter 类，使其在浏览器中可通过 window.EventEmitter 访问
  window.EventEmitter = EventEmitter;
