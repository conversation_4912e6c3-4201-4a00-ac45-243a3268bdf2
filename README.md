# Cantos - 山海经题材开放式MMORPG

## 项目概述

**Cantos** 是一款基于中国古典文献《山海经》的开放世界MMORPG网页游戏。游戏致力于通过现代Web技术重现山海经中描述的神秘世界，让玩家在探索中感受中华文化的深厚底蕴。

### 核心特色
- 🏔️ **山海经世界观**: 忠实还原古典文献中的地理和神兽
- 🎮 **开放世界探索**: 大型无缝世界，自由探索
- 🐉 **神兽系统**: 丰富的神兽生态和互动机制
- 🌅 **文化教育**: 寓教于乐，传承中华文化
- 🌐 **Web原生**: 无需下载，浏览器直接游玩

## 技术栈

### 前端技术
- **图形引擎**: Babylon.js 8.11.0
- **物理引擎**: Havok Physics
- **开发语言**: JavaScript (ES6+)
- **构建工具**: Webpack
- **网络通信**: WebSocket + 自定义二进制协议

### 后端技术
- **服务端语言**: Common Lisp
- **数据库**: PostgreSQL
- **通信协议**: WebSocket + conspack序列化

## 当前状态

### ✅ 已完成功能
- [x] Babylon.js场景初始化和渲染循环
- [x] Havok物理引擎集成
- [x] WebSocket网络管理器(支持自动重连、心跳机制)
- [x] 玩家实体系统(本地/远程玩家区分)
- [x] 输入管理系统(WASD移动 + 空格跳跃)
- [x] 基础相机控制(第三人称弧度旋转相机)
- [x] 二进制消息序列化/反序列化
- [x] 玩家状态同步(位置、旋转)

### 🚧 开发中功能
- [ ] 地形生成系统
- [ ] 环境和天气系统
- [ ] 资源管理系统
- [ ] 角色外观系统

## 开发计划

详细的开发路线图请参考 [开发路线图文档](doc/development-roadmap.md)

### 第一阶段：游戏世界基础 (4-6周)
- 地形和环境系统
- 资源和资产管理
- 场景管理优化

### 第二阶段：角色系统深化 (3-4周)
- 角色外观和动画
- 角色属性系统

### 第三阶段：交互系统 (3-4周)
- UI系统
- 物品和背包系统

### 第四阶段：游戏机制 (4-5周)
- 战斗系统基础
- NPC和任务系统

### 第五阶段：社交和多人功能 (3-4周)
- 聊天和社交系统
- 公会系统基础

### 第六阶段：优化和完善 (2-3周)
- 性能优化
- 用户体验优化
- 测试和调试

## 文档结构

- 📋 [开发路线图](doc/development-roadmap.md) - 完整的开发计划和里程碑
- 🔧 [技术规范](doc/technical-specifications.md) - 详细的技术实现规范
- 📅 [第一阶段实施计划](doc/phase1-implementation-plan.md) - 第一阶段的详细任务分解
- 🎨 [山海经主题设计](doc/shanhaijing-theme-design.md) - 游戏主题和文化设计
- 📊 [项目管理](doc/project-management.md) - 项目管理和质量保证

## 快速开始

### 环境要求
- Node.js 16+
- 现代浏览器(支持WebGL 2.0)
- 网络连接(用于服务器通信)

### 安装和运行
```bash
# 安装依赖
npm install

# 启动开发服务器
npm start

# 构建生产版本
npm run build

# 运行测试
npm run test:unit
npm run test:visuals
```

### 开发服务器
- 开发服务器: http://localhost:8080
- WebGL版本: http://localhost:8080
- WebGPU版本: http://localhost:8080/?engine=webgpu

## 项目结构

```
src/
├── index.js              # 主入口文件
├── entities/             # 游戏实体
│   └── player.js         # 玩家实体
├── network/              # 网络通信
│   ├── networkManager.js # 网络管理器
│   └── protocal.js       # 协议定义
├── input/                # 输入管理
│   └── inputManager.js   # 输入管理器
├── utils/                # 工具函数
├── conspack/             # 二进制序列化
└── scenes/               # 场景模板
```

## 开发指南

### 代码规范
- 使用ES6+语法和模块系统
- 遵循驼峰命名规则
- 所有注释使用中文
- 保持代码模块化和可测试性

### 提交规范
```bash
feat(terrain): 添加地形生成器核心功能
fix(network): 修复WebSocket重连问题
docs(readme): 更新项目文档
test(player): 添加玩家移动测试用例
```

## 贡献指南

由于这是一个单人开发项目，暂不接受外部贡献。但欢迎：
- 🐛 报告Bug和问题
- 💡 提出功能建议
- 📝 文档改进建议
- 🎨 美术资源建议

## 许可证

本项目采用私有许可证，仅供学习和研究使用。

## 联系方式

- **开发者**: He Xian-zhi
- **邮箱**: <EMAIL>
- **项目地址**: https://gitee.com/hxz/cantos-client

---

*让我们一起在数字世界中重现山海经的神奇！* 🏔️✨
